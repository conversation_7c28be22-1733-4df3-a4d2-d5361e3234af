import { GoogleAnalytics } from "@next/third-parties/google";
import type { Metadata } from "next";
import "./globals.scss";
import { imageUrl } from "@/utils/image-url";

export const metadata: Metadata = {
  icons: imageUrl("/favicon.png"),
  title: "【威金森】- 超激夏祭活動",
  description:
    "快來挑戰【威金森超激夏祭活動】就有機會獲得威金森碳酸水兌換碼！成功闖三關還有PS5 PRO大獎等你拿！",
  keywords:
    "威金森,氣泡水,碳酸水,朝日,asahi,超激夏祭,兌換碼,PS5,PS5PRO,極限平衡,威風接招,威金森考驗",
  openGraph: {
    images: { url: imageUrl("/share-image.jpg"), height: 630, width: 1200 },
    type: "website",
    locale: "zh_TW",
    siteName: "【威金森】- 超激夏祭活動",
    title: "【威金森】- 超激夏祭活動",
    description:
      "快來挑戰【威金森超激夏祭活動】就有機會獲得威金森碳酸水兌換碼！成功闖三關還有PS5 PRO大獎等你拿！",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">{children}</body>
      <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GOOGLE_GA_ID!} />
    </html>
  );
}
