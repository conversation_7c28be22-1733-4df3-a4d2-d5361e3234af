import { DialogBase } from "@/app/components/dialog-base";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";

export const SuccessDialog = ({ onClose }: { onClose: () => void }) => {
  return (
    <DialogBase visible>
      <div className="w-[79vw] aspect-[851/1262] relative z-0 flex flex-col items-center text-center">
        <Image
          src={imageUrl("/popup-normal.png")}
          alt=""
          className="absolute -z-10 top-0 left-0 w-full ml-[1.3vw]"
          unoptimized
          unselectable="on"
          width={851}
          height={1262}
        />
        <div className="text-[8.5vw] text-[#fff100] font-[1000] mt-[31vw]">
          已收到您的資料
        </div>
        <div className="text-[4.3vw] font-bold mt-[6vw]">激夏大獎得獎名單</div>
        <div className="text-[4.3vw] font-bold mt-[2vw]">將於2025/8/25公告</div>

        <button
          className="absolute top-0 right-0 w-[9vw] h-[9vw]"
          onClick={onClose}
        />
      </div>
    </DialogBase>
  );
};
