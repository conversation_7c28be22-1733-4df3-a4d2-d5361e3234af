"use client";
import { FullScreenImage } from "@/app/components/full-screen";
import { useState } from "react";
import { imageUrl } from "@/utils/image-url";
import { TexturedText } from "@/app/components/textured-text";
import Image from "next/image";
import clsx from "clsx";
import { ContentArea } from "../../../components/content-area";
import { useQuery } from "@tanstack/react-query";

const config = [
  {
    gameId: "balance" as const,
    image: "/tab-game-balance.png",
  },
  {
    gameId: "catch" as const,
    image: "/tab-game-catch.png",
  },
  {
    gameId: "quiz" as const,
    image: "/tab-game-quiz.png",
  },
];

type TabType = (typeof config)[number]["gameId"];

// Types for leaderboard data
interface LeaderboardRecord {
  id: string;
  score: number;
  nickname?: string;
  user: {
    id: string;
    nickname?: string;
  };
}

interface LeaderboardEntry {
  rank: number;
  name: string;
  score: number;
}

// Service for fetching leaderboard data
const leaderboardService = {
  getLeaderboard: async (gameId: string): Promise<LeaderboardRecord[]> => {
    const response = await fetch(`/api/record/list?gameId=${gameId}`);

    if (!response.ok) {
      throw new Error("獲取排行榜失敗");
    }

    return response.json();
  },
};

// React Query hook for leaderboard data
const useLeaderboard = (gameId: string) => {
  return useQuery({
    queryKey: ["leaderboard", gameId],
    queryFn: () => leaderboardService.getLeaderboard(gameId),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
  });
};

// Helper function to transform API data to display format
const transformLeaderboardData = (
  records: LeaderboardRecord[],
): LeaderboardEntry[] => {
  return records.map((record, index) => ({
    rank: index + 1,
    name: record.nickname || record.user.nickname || "匿名用戶",
    score: Number(record.score),
  }));
};

const GamePage = () => {
  const [currentTab, setCurrentTab] = useState<TabType>("balance");

  // Fetch leaderboard data for current tab
  const {
    data: leaderboardRecords,
    isLoading,
    error,
  } = useLeaderboard(currentTab);

  // Transform data for display
  const leaderboardData = leaderboardRecords
    ? transformLeaderboardData(leaderboardRecords)
    : [];

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game-entries.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[34vw]">
        <TexturedText
          className="text-[8.2vw] font-[1000] italic px-[3vw] tracking-[0.5vw] mb-[3vw]"
          color="yellow"
        >
          強氣王者榜
        </TexturedText>

        <div className="flex gap-[2vw]">
          {config.map(({ gameId, image }) => {
            return (
              <button
                key={gameId}
                className="relative w-[24vw] aspect-[2.2]"
                onClick={() => setCurrentTab(gameId)}
              >
                <Image
                  unoptimized
                  alt=""
                  className="absolute left-0 top-0 w-full"
                  src={imageUrl("/tab-normal.png")}
                  width={271}
                  height={123}
                />
                <Image
                  unoptimized
                  alt=""
                  className={clsx(
                    "absolute left-0 top-0 w-full opacity-0 transition-all",
                    {
                      "opacity-100": currentTab === gameId,
                    },
                  )}
                  src={imageUrl("/tab-active.png")}
                  width={271}
                  height={123}
                />

                <Image
                  unoptimized
                  alt=""
                  className="absolute left-1/2 top-5/11 -translate-x-1/2 -translate-y-1/2 w-[90%]"
                  src={imageUrl(image)}
                  width={271}
                  height={123}
                />
              </button>
            );
          })}
        </div>
        <ContentArea className="h-[93vw] px-[3vw] text-[4vw]">
          <table className="w-full text-center">
            <thead className="font-[1000] leading-[3]">
              <tr>
                <th>排名</th>
                <th>暱稱</th>
                <th>{currentTab === "balance" ? "秒數" : "分數"} </th>
              </tr>
            </thead>
            <tbody className="font-medium leading-[1.65]">
              {isLoading ? (
                <tr>
                  <td colSpan={3} className="text-white">
                    載入中...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={3} className="text-red-400">
                    載入失敗，請重新整理頁面
                  </td>
                </tr>
              ) : leaderboardData.length === 0 ? (
                <tr>
                  <td colSpan={3} className="text-white">
                    暫無排行榜資料
                  </td>
                </tr>
              ) : (
                leaderboardData.map(({ rank, name, score }) => {
                  return (
                    <tr key={rank}>
                      <td>{rank}</td>
                      <td className="max-w-[30vw] overflow-hidden text-ellipsis">
                        {name}
                      </td>
                      <td>{score}</td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
          {(leaderboardRecords?.length ?? 0) >= 10 && (
            <div className="font-medium text-[#fff100] text-[3vw] mt-[1vw]">
              *取最佳成績
            </div>
          )}
        </ContentArea>
      </div>
    </>
  );
};

export default GamePage;
