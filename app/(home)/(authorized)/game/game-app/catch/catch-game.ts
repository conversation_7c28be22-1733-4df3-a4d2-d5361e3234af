import { Vec2 } from "../shared/vec2";
import { catchItems } from "./catch-item-list";

export interface CatchItem {
  type: "brand" | "other";
  image: { src: string; width: number; height: number };
  position: Vec2;
  velocity: Vec2;
}
export interface CatchGameState {
  passedTime: number;
  score: number;
  items: (CatchItem & {
    id: string;
  })[];

  player: {
    position: Vec2;
    height: number;
    width: number;
  };
}

export class CatchGame {
  startTime = Date.now();
  animationRequestId = 0;
  passedTime = 0;
  score = 0;
  items: (CatchItem & {
    id: string;
  })[] = [];
  lastItemSpawnTime = 0;

  player = {
    position: new Vec2(50, 70),
    height: 10,
    width: 30,
  };

  onUpdateUi: (state: CatchGameState) => void;
  onGameEnd: (score: number) => void;

  constructor({
    onUpdateUi,
    onGameEnd,
  }: {
    onUpdateUi: (state: CatchGameState) => void;
    onGameEnd: (score: number) => void;
  }) {
    this.onUpdateUi = onUpdateUi;
    this.onGameEnd = onGameEnd;

    onUpdateUi({
      passedTime: this.passedTime,
      score: this.score,
      items: this.items,
      player: this.player,
    });
  }

  start = () => {
    setTimeout(() => {
      this.startTime = Date.now();
      this.lastItemSpawnTime = 0;
      this.items = [];
      this.score = 0;
      this.lastItemSpawnTime = 0;
      this.passedTime = 0;
      this.addItem();
      this.animationRequestId = requestAnimationFrame(this.update);
    }, 500);
  };

  onGameOver = () => {
    this.onGameEnd(this.score);
  };

  onUpdatePlayerPosition = (position: number) => {
    this.player.position.x = position;
  };

  update = () => {
    this.passedTime = Date.now() - this.startTime;

    this.updateItems();
    this.checkCollisions();

    this.onUpdateUi({
      passedTime: this.passedTime,
      score: this.score,
      items: this.items,
      player: this.player,
    });

    const isTimeout = this.passedTime >= 60 * 1000;

    if (isTimeout) {
      window.cancelAnimationFrame(this.animationRequestId);
      this.onGameOver();
      return;
    } else {
      this.animationRequestId = requestAnimationFrame(this.update);
    }
  };

  updateItems = () => {
    this.items = this.items.map((item) => {
      item.position.y += item.velocity.y;
      return item;
    });

    // Remove items that have fallen off the screen
    this.items = this.items.filter((item) => item.position.y < 110);

    // Spawn items based on time intervals instead of random chance
    this.checkItemSpawn();
  };

  checkItemSpawn = () => {
    // Calculate spawn interval based on game progression
    // Start with 2 seconds, decrease to 0.5 seconds over 60 seconds
    const gameProgressRatio = Math.min(this.passedTime / 60000, 1); // 0 to 1 over 60 seconds
    const minInterval = 500; // 0.5 seconds minimum
    const maxInterval = 2000; // 2 seconds maximum
    const baseInterval =
      maxInterval - gameProgressRatio * (maxInterval - minInterval);

    // Add slight variation (±20%) to make it less predictable
    const variation = 0.2;
    const randomFactor = 1 + (Math.random() - 0.5) * variation;
    const currentInterval = baseInterval * randomFactor;

    // Check if enough time has passed since last spawn
    const timeSinceLastSpawn = this.passedTime - this.lastItemSpawnTime;

    if (timeSinceLastSpawn >= currentInterval) {
      this.addItem();
      this.lastItemSpawnTime = this.passedTime;
    }
  };

  checkCollisions = () => {
    this.items = this.items.filter((item) => {
      // Use percentage-based collision detection
      const horizontalDistance = Math.abs(
        item.position.x - this.player.position.x,
      );
      const verticalDistance = Math.abs(
        item.position.y - this.player.position.y,
      );

      // Collision thresholds in percentage terms
      const horizontalThreshold = this.player.width / 2 + 3; // 3% buffer for easier catching
      const verticalThreshold = this.player.height / 2 + 2; // 2% buffer

      const isColliding =
        horizontalDistance < horizontalThreshold &&
        verticalDistance < verticalThreshold &&
        item.position.y > 75; // Only check collision when item is near the bottom

      if (isColliding) {
        // Different points for different item types
        if (item.type === "brand") {
          this.score += 100; // Brand items worth more points
        } else {
          this.score += 30;
        }
        return false; // Remove the item
      } else {
        return true; // Keep the item
      }
    });
  };

  addItem = () => {
    const item = catchItems[Math.floor(Math.random() * catchItems.length)];
    // Spawn items across the full width, starting above the screen
    const position = new Vec2(
      Math.random() * 90 + 5, // 5% to 95% to avoid edges
      -Math.random() * 20 - 10, // Start well above the screen
    );
    // Variable velocity based on game time (gets faster)
    const baseVelocity = (this.passedTime / 1000) * 0.02;
    const velocity = new Vec2(0, Math.random() * 1 + baseVelocity);

    this.items.push({
      id: Math.random().toString(36).substring(7),
      type: item.type,
      image: {
        src: item.image,
        width: item.width,
        height: item.height,
      },
      position,
      velocity,
    });
  };
}
