import { useEffect, useMemo, useState } from "react";
import { CatchGame, CatchGameState } from "./catch-game";
import { CatchGameView } from "./catch-game-view";

export const CatchGameApp = ({
  onGameEnd,
}: {
  onGameEnd: (score: number) => void;
}) => {
  const [data, setData] = useState<CatchGameState>();

  const gameApp = useMemo(() => {
    return new CatchGame({
      onUpdateUi: setData,
      onGameEnd: onGameEnd,
    });
  }, [onGameEnd]);

  useEffect(() => {
    gameApp.start();
  }, [gameApp]);

  return (
    <CatchGameView
      gameState={data}
      onUpdatePlayerPosition={gameApp.onUpdatePlayerPosition}
    />
  );
};
