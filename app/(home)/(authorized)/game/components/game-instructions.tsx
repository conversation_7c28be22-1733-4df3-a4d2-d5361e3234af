import { GameId } from "@/app/constants";

interface GameInstructionsProps {
  gameId: GameId;
}

export const GameInstructions = ({ gameId }: GameInstructionsProps) => {
  switch (gameId) {
    case "balance":
      return (
        <>
          限時1分鐘
          <br />
          左右移動控制威金森碳酸水
          <br />
          讓上方滾動的瓶蓋維持平衡
          <br />
          堅持越久「超威積分」越高
        </>
      );
    case "catch":
      return (
        <>
          限時1分鐘
          <br />
          移動下方威金森LOGO
          <br />
          接住「威金森品牌相關元素」
          <br />
          接越多「超威積分」越高
        </>
      );
    case "quiz":
      return (
        <>
          資深威粉來喊聲
          <br />
          挑戰經典威金森問答
          <span className="inline-block mx-[-0.25em]">！</span>
          <br />
          每關限時10秒，共5小關
          <br />
          答對一題得20分
          <br />
          答錯則倒扣10分
        </>
      );
    default:
      return null;
  }
};
