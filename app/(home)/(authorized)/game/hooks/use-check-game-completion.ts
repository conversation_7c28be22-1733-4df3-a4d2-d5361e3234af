"use client";
import { useQuery } from "@tanstack/react-query";

// 檢查用戶是否完成全部遊戲
const checkGameCompletion = async () => {
  const response = await fetch("/api/game/list");
  const gameList = await response.json();
  return Object.keys(gameList).every(
    (gameId) => gameList[gameId].GameRecord.length > 0,
  );
};

export const useCheckGameCompletion = () => {
  const { data } = useQuery({
    queryFn: checkGameCompletion,
    queryKey: ["game", "completion"],
  });

  return data;
};
