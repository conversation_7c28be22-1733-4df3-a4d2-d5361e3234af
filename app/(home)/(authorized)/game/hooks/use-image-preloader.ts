import { useState, useCallback } from "react";
import { imageUrl } from "@/utils/image-url";

export const LOAD_IMAGE_STATE = {
  finished: 0,
};

// All images used throughout the game experience
const GAME_IMAGES = [
  // Background images
  "/screen-login.png",
  "/screen-game-entries.png",
  "/screen-game.png",
  "/game-instruction-dialog.png",
  "/game-result-background.png",

  // Game entry buttons
  "/btn-game1-play-now.png",
  "/btn-game1-play-again.png",
  "/btn-game2-play-now.png",
  "/btn-game2-play-again.png",
  "/btn-game2-coming-soon.png",
  "/btn-game3-play-now.png",
  "/btn-game3-play-again.png",
  "/btn-game3-coming-soon.png",

  // Game titles
  "/game-title-balance.png",
  "/game-title-catch.png",
  "/game-title-quiz.png",

  // Balance game assets
  "/game-score-background.png",
  "/game-balance-cap.png",
  "/game-balance-direction.png",
  "/game-balance-water.png",

  // UI buttons
  "/button-app.png",
  "/button-buy.png",
  "/button-wide.png",

  // Catch game assets
  "/game-catch-asset-1.png",
  "/game-catch-asset-2.png",
  "/game-catch-asset-3.png",
  "/game-catch-asset-4.png",
  "/game-catch-asset-5.png",
  "/game-catch-asset-6.png",
  "/game-catch-asset-7.png",
  "/game-catch-asset-8.png",
  "/game-catch-asset-9.png",
  "/logo-button.png",

  // Catch game assets
  "/game-quiz-option.png",

  // Dialog images
  "/popup-luckydraw-reward.png",
  "/popup-normal.png",
];

interface PreloaderState {
  isLoading: boolean;
  progress: number;
  loadedCount: number;
  totalCount: number;
  isComplete: boolean;
  hasStarted: boolean;
  startTime: number;
}

export const useImagePreloader = () => {
  const [state, setState] = useState<PreloaderState>({
    isLoading: false,
    progress: 0,
    loadedCount: 0,
    totalCount: GAME_IMAGES.length,
    isComplete: false,
    hasStarted: false,
    startTime: 0,
  });

  const preloadImages = useCallback(() => {
    const startTime = Date.now();
    setState((prev) => {
      if (prev.hasStarted) return prev; // Prevent multiple starts
      return {
        ...prev,
        isLoading: true,
        isComplete: false,
        hasStarted: true,
        startTime,
      };
    });

    let loadedCount = 0;
    const totalCount = GAME_IMAGES.length;
    const MIN_LOADING_TIME = 1000; // Minimum 1 seconds

    const checkCompletion = () => {
      const elapsedTime = Date.now() - startTime;
      const allImagesLoaded = loadedCount === totalCount;
      const minTimeElapsed = elapsedTime >= MIN_LOADING_TIME;

      if (allImagesLoaded) {
        LOAD_IMAGE_STATE.finished += 1;

        if (minTimeElapsed) {
          setState((prev) => ({
            ...prev,
            isComplete: true,
            isLoading: false,
          }));
        } else if (!minTimeElapsed) {
          // Wait for minimum time to elapse
          setTimeout(() => {
            setState((prev) => ({
              ...prev,
              isComplete: true,
              isLoading: false,
            }));
          }, MIN_LOADING_TIME - elapsedTime);
        }
      }
    };

    const updateProgress = () => {
      loadedCount++;
      const progress = Math.round((loadedCount / totalCount) * 100);

      setState((prev) => ({
        ...prev,
        loadedCount,
        progress,
      }));

      checkCompletion();
    };

    // Preload all images
    GAME_IMAGES.forEach((imagePath) => {
      const img = new Image();

      img.onload = updateProgress;
      img.onerror = updateProgress; // Still count as "loaded" to prevent hanging

      img.src = imageUrl(imagePath);
    });
  }, []);

  return {
    ...state,
    preloadImages,
  };
};
