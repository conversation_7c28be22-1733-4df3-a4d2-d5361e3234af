import { GameId } from "@/app/constants";

const DISMISSED_DIALOG = "DISMISSED_DIALOG";

export const getDialogConfig = () => {
  try {
    return JSON.parse(localStorage.getItem(DISMISSED_DIALOG) ?? "{}");
  } catch {
    return {};
  }
};

export type DialogType = GameId | "all-games-available" | "all-games-complete";

export const neverShowDialog = (dialogId: DialogType) => {
  const dismissedDialogConfig = getDialogConfig();
  localStorage.setItem(
    DISMISSED_DIALOG,
    JSON.stringify({ ...dismissedDialogConfig, [dialogId]: true }),
  );
};
