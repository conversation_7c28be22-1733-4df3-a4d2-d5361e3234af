import { FullScreenImage } from "@/app/components/full-screen";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { BalanceGameApp } from "../game-app/balance";
import { CatchGameApp } from "../game-app/catch";
import { QuizGameApp } from "../game-app/quiz";
import { GameTitle } from "../components/game-title";
import { useCallback, useEffect, useRef } from "react";

interface PlayStageProps {
  gameId: GameId;
  onGameEnd: (
    score: number,
    gameDuration: number,
    gameSessionToken: string,
  ) => void;
}

export const PlayStage = ({ gameId, onGameEnd }: PlayStageProps) => {
  const gameSessionToken = useRef("");
  const gameStartTime = useRef(0);

  // Fetch game session token when component mounts
  useEffect(() => {
    const fetchGameSession = async () => {
      try {
        const response = await fetch("/api/game/session", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ gameId }),
        });

        if (response.ok) {
          const data = await response.json();
          gameSessionToken.current = data.gameSessionToken;
          gameStartTime.current = Date.now();
        } else {
          console.error("Failed to fetch game session");
        }
      } catch (error) {
        console.error("Error fetching game session:", error);
      }
    };

    fetchGameSession();
  }, [gameId]);

  const handleGameEnd = useCallback(
    (score: number) => {
      const gameDuration = Date.now() - gameStartTime.current;
      onGameEnd(score, gameDuration, gameSessionToken.current);
    },
    [onGameEnd],
  );
  const renderGameApp = () => {
    // Don't render game until we have a session token
    if (!gameSessionToken) {
      return (
        <div className="text-center text-[4vw] font-bold">載入遊戲中...</div>
      );
    }

    switch (gameId) {
      case "balance":
        return <BalanceGameApp onGameEnd={handleGameEnd} />;
      case "catch":
        return <CatchGameApp onGameEnd={handleGameEnd} />;
      case "quiz":
        return <QuizGameApp onGameEnd={handleGameEnd} />;
      default:
        return (
          <div className="text-center text-[4vw] font-bold">Game Not Found</div>
        );
    }
  };

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <div className="w-[73vw] h-[23.5vw] flex justify-center items-center">
          <GameTitle gameId={gameId} />
        </div>
        <div>{renderGameApp()}</div>
      </div>
    </>
  );
};
