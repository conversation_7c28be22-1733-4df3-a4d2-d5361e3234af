import { FullScreenImage } from "@/app/components/full-screen";
import { TexturedText } from "@/app/components/textured-text";
import { imageUrl } from "@/utils/image-url";

interface LoadingStageProps {
  progress: number;
}

export const LoadingStage = ({ progress }: LoadingStageProps) => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-login.png")} />
      <div className="flex flex-col items-center relative h-full pt-[56.5vw]">
        <div className="flex flex-col items-center">
          <TexturedText className="font-[1000] text-[6vw] mb-[8vw]">
            遊戲載入中...
          </TexturedText>

          {/* Progress bar */}
          <div className="w-[60vw] h-[2vw] bg-gray-300 rounded-full overflow-hidden mb-[4vw]">
            <div
              className="h-full bg-white transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>

          <TexturedText className="font-[700] text-[4vw] text-[#fff100]">
            {progress}%
          </TexturedText>
        </div>
      </div>
    </>
  );
};
