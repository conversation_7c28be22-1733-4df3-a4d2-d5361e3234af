"use client";

import { useState } from "react";
import { Mask } from "./mask";
import Image from "next/image";
import clsx from "clsx";
import Link from "next/link";
import { imageUrl } from "@/utils/image-url";

const linkConfig = [
  { text: "首頁", href: "/home" },
  { text: "個人頁面", href: "/personal" },
  { text: "得獎名單", href: "/winner" },
  { text: "排行榜", href: "/leaderboard" },
  { text: "活動辦法", href: "/rule" },
  { text: "購買通路", href: "/channel" },
  { text: "品牌廣告", href: "/cm" },
];

export const Menu = () => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Mask visible={visible} className="z-30" />

      <div
        className={clsx(
          "absolute z-30 top-[30vw] right-0 w-[26vw] h-[140vw] overflow-hidden",
          { ["pointer-events-none"]: !visible },
        )}
      >
        <div
          className={clsx("relative transition-all left-0", {
            ["left-[26vw]"]: !visible,
          })}
        >
          <Image
            unoptimized
            className="absolute top-0 right-0 w-full h-auto"
            src={imageUrl("/menu-background.png")}
            alt=""
            width={282}
            height={1489}
          />
          <ul
            className={clsx(
              "pt-[13vw] px-[4vw]",
              "relative z-30 py-[5vw] flex flex-col gap-[4.5vw]",
              "text-white font-[1000] text-[4.3vw]",
            )}
          >
            {linkConfig.map(({ href, text }) => (
              <li
                className="py-[2vw]"
                key={href}
                onClick={() => setVisible(false)}
              >
                <Link key={href} href={href}>
                  {text}
                </Link>
              </li>
            ))}

            <li className="flex justify-center gap-[2vw]">
              <a
                href="https://www.facebook.com/wilkinsontw"
                target="_blank"
                className="w-[7vw]"
              >
                <Image
                  unoptimized
                  src={imageUrl("/logo-facebook.png")}
                  width={30}
                  height={30}
                  alt="Facebook"
                />
              </a>
              <a
                href="https://asahisoftdrinks.com.tw"
                target="_blank"
                className="w-[7vw]"
              >
                <Image
                  unoptimized
                  src={imageUrl("/logo-wilkinson.png")}
                  width={30}
                  height={30}
                  alt="Instagram"
                />
              </a>
            </li>
          </ul>
        </div>
      </div>

      <div className="absolute right-[2.5vw] top-[1.5vw] z-30">
        <button
          className="w-[14vw] h-[14vw] flex flex-col justify-center items-center gap-[2vw] px-[4vw] py-[5vw]"
          onClick={() => {
            setVisible((prev) => !prev);
          }}
        >
          <span
            className={clsx(
              "transition-transform relative block w-full h-0 shadow-[0_0_0_0.44vw]",
              {
                ["-rotate-45 translate-y-[2vw]"]: visible,
              },
            )}
          />
          <span
            className={clsx(
              "transition-all relative block w-full h-0 shadow-[0_0_0_0.44vw]",
              {
                ["-rotate-45 opacity-0"]: visible,
              },
            )}
          />
          <span
            className={clsx(
              "transition-transform relative block w-full h-0 shadow-[0_0_0_0.44vw]",
              {
                ["rotate-45 translate-y-[-2vw]"]: visible,
              },
            )}
          />
        </button>
      </div>
    </>
  );
};
