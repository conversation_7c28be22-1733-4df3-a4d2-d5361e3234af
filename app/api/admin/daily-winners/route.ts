import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  const date = req.nextUrl.searchParams.get("date");

  if (!date) {
    return NextResponse.json({ message: "缺少日期參數" }, { status: 400 });
  }

  try {
    const start = new Date(date);
    const end = new Date(date);
    end.setDate(end.getDate() + 1);

    const winners = await prisma.coupon.findMany({
      where: {
        createdAt: {
          gte: start,
          lt: end,
        },
        userId: {
          not: null,
        },
      },
      include: {
        User: {
          select: {
            id: true,
            nickname: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const formattedWinners = winners.map((coupon) => ({
      id: coupon.id,
      code: coupon.code,
      createdAt: coupon.createdAt.toISOString(),
      user: coupon.User,
    }));

    return NextResponse.json(formattedWinners);
  } catch (error) {
    console.error("Error fetching daily winners:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
