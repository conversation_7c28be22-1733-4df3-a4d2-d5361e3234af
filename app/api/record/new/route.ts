import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";
import crypto from "crypto";

// Game-specific score limits and time constraints
const GAME_LIMITS = {
  balance: {
    maxScore: 60, // Maximum 60 seconds
    minTime: 1000, // Minimum 1 second
    maxTime: 65000, // Maximum 65 seconds (with buffer)
  },
  catch: {
    maxScore: 5000, // Realistic maximum for 60 seconds
    minTime: 55000, // Minimum 55 seconds (game should run close to 60s)
    maxTime: 65000, // Maximum 65 seconds (with buffer)
  },
  quiz: {
    maxScore: 100, // Adjust based on quiz implementation
    minTime: 1000, // Minimum 1 seconds
    maxTime: 60000, // Maximum 60 seconds
  },
} as const;

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return NextResponse.json({ message: "請先登入" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { gameId, score, nickname, gameSessionToken, gameDuration } = body;

    // Validate required fields
    if (
      !gameId ||
      score === undefined ||
      !nickname ||
      !gameSessionToken ||
      !gameDuration
    ) {
      return NextResponse.json({ message: "缺少必要參數" }, { status: 400 });
    }

    // Validate game ID
    if (!GAME_LIMITS[gameId as keyof typeof GAME_LIMITS]) {
      return NextResponse.json({ message: "無效的遊戲ID" }, { status: 400 });
    }

    const limits = GAME_LIMITS[gameId as keyof typeof GAME_LIMITS];

    // Validate score limits
    if (score < 0 || score > limits.maxScore) {
      // Log suspicious score
      console.warn(`Suspicious score submitted by user ${userId}`, {
        gameId,
        score,
        maxAllowed: limits.maxScore,
        timestamp: new Date().toISOString(),
      });

      return NextResponse.json(
        { message: "分數超出合理範圍" },
        { status: 400 },
      );
    }

    // Validate game duration
    if (gameDuration < limits.minTime || gameDuration > limits.maxTime) {
      return NextResponse.json({ message: "遊戲時間異常" }, { status: 400 });
    }

    // Verify game session token
    const expectedToken = generateGameSessionToken(userId, gameId);
    if (gameSessionToken !== expectedToken) {
      // Log suspicious activity
      console.warn(
        `Invalid game session token for user ${userId}, game ${gameId}`,
        {
          provided: gameSessionToken,
          expected: expectedToken,
          timestamp: new Date().toISOString(),
        },
      );

      return NextResponse.json({ message: "無效的遊戲會話" }, { status: 400 });
    }

    // Create the game record
    const gameRecord = await prisma.gameRecord.create({
      data: {
        gameId,
        userId,
        score,
        nickname,
      },
    });

    return NextResponse.json({
      id: gameRecord.id,
      message: "記錄創建成功",
    });
  } catch (error) {
    console.error("Error creating game record:", error);
    return NextResponse.json(
      {
        message: "伺服器錯誤，請稍後再試",
      },
      { status: 500 },
    );
  }
}

// Generate a secure game session token
function generateGameSessionToken(userId: string, gameId: string): string {
  const secret = process.env.NEXTAUTH_SECRET || "fallback-secret";
  const data = `${userId}-${gameId}-${Math.floor(Date.now() / 300000)}`; // 5-minute window
  return crypto.createHmac("sha256", secret).update(data).digest("hex");
}
