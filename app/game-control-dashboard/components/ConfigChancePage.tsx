"use client";
import React, { useEffect } from "react";
import {
  Card,
  Form,
  InputNumber,
  Button,
  Space,
  Divider,
  Statistic,
  Row,
  Col,
  DatePicker,
  Input,
} from "antd";
import { SaveOutlined, ReloadOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import {
  useWebsiteConfig,
  useDrawStats,
  useUpdateWebsiteConfig,
  useRefreshConfig,
} from "../hooks/useConfig";

const { RangePicker } = DatePicker;

interface FormValues {
  name: string;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs];
  luckyDrawChance: number;
}

export const ConfigChancePage: React.FC = () => {
  const [form] = Form.useForm();

  // React Query hooks
  const { data: config, isLoading: configLoading } = useWebsiteConfig();
  const { data: stats, isLoading: statsLoading } = useDrawStats();
  const updateConfigMutation = useUpdateWebsiteConfig();
  const refreshConfig = useRefreshConfig();

  // Update form values when config data is loaded
  useEffect(() => {
    if (config) {
      form.setFieldsValue({
        name: config.name,
        dateRange: [dayjs(config.startDate), dayjs(config.endDate)],
        luckyDrawChance: config.luckyDrawChance * 100, // Convert to percentage
      });
    }
  }, [config, form]);

  const handleSubmit = (values: FormValues) => {
    const payload = {
      name: values.name,
      startDate: values.dateRange[0].toISOString(),
      endDate: values.dateRange[1].toISOString(),
      luckyDrawChance: values.luckyDrawChance / 100, // Convert back to decimal
    };

    updateConfigMutation.mutate(payload);
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">設定 PIN 碼中獎機率</h2>

      {stats && (
        <Row gutter={16} className="mb-6">
          <Col span={6}>
            <Card>
              <Statistic
                title="總註冊用戶"
                value={stats.totalUsers}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="總抽獎次數"
                value={stats.totalDraws}
                valueStyle={{ color: "#722ed1" }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="總中獎次數"
                value={stats.totalWins}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="實際中獎率"
                value={stats.actualWinRate}
                precision={2}
                suffix="%"
                valueStyle={{ color: "#f5222d" }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card title="活動設定">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            luckyDrawChance: 10, // Default 10%
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="活動名稱"
                name="name"
                rules={[{ required: true, message: "請輸入活動名稱" }]}
              >
                <Input placeholder="請輸入活動名稱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="抽獎活動期間"
                name="dateRange"
                rules={[{ required: true, message: "請選擇活動期間" }]}
              >
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="中獎機率 (%)"
                name="luckyDrawChance"
                rules={[
                  { required: true, message: "請設定中獎機率" },
                  {
                    type: "number",
                    min: 0,
                    max: 100,
                    message: "機率必須在 0-100 之間",
                  },
                ]}
                extra="設定每次抽獎的中獎機率，範圍 0-100%"
              >
                <InputNumber
                  min={0}
                  max={100}
                  precision={2}
                  style={{ width: "100%" }}
                  addonAfter="%"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={updateConfigMutation.isPending}
              >
                儲存設定
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={refreshConfig}
                loading={configLoading || statsLoading}
              >
                重新整理
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {config && (
        <div className="mt-6">
          <Card title="當前配置資訊" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <p>
                  <strong>活動名稱:</strong> {config.name}
                </p>
              </Col>
              <Col span={8}>
                <p>
                  <strong>開始時間:</strong>{" "}
                  {dayjs(config.startDate).format("YYYY-MM-DD HH:mm:ss")}
                </p>
              </Col>
              <Col span={8}>
                <p>
                  <strong>結束時間:</strong>{" "}
                  {dayjs(config.endDate).format("YYYY-MM-DD HH:mm:ss")}
                </p>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <p>
                  <strong>中獎機率:</strong>{" "}
                  {(config.luckyDrawChance * 100).toFixed(2)}%
                </p>
              </Col>
              <Col span={8}>
                <p>
                  <strong>最後更新:</strong>{" "}
                  {dayjs(config.updatedAt).format("YYYY-MM-DD HH:mm:ss")}
                </p>
              </Col>
            </Row>
          </Card>
        </div>
      )}
    </div>
  );
};
