"use client";
import React, { useState } from "react";
import { Table, DatePicker, Button, Space } from "antd";
import { DownloadOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { useDailyWinners } from "../hooks/useDailyReport";
import type { WinnerRecord } from "../services/dailyReportService";

const columns: ColumnsType<WinnerRecord> = [
  {
    title: "日期",
    dataIndex: "createdAt",
    key: "createdAt",
    render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm:ss"),
    sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
  },
  {
    title: "用戶名",
    dataIndex: ["user", "nickname"],
    key: "nickname",
    render: (nickname: string, record) =>
      nickname || record.user?.email || record.user?.id || "未設定",
  },
  {
    title: "PIN碼",
    dataIndex: "code",
    key: "code",
  },
  {
    title: "用戶ID",
    dataIndex: ["user", "id"],
    key: "userId",
  },
];

export const DailyReportPage: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(dayjs().startOf("day"));

  // React Query for fetching daily winners
  const { data, isLoading } = useDailyWinners({
    date: selectedDate.toISOString(),
  });

  const exportToCSV = () => {
    if (!data) return;

    const csvContent = [
      ["日期", "Email", "PIN碼", "用戶ID"],
      ...data.map((record) => [
        dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss"),
        record.user?.email || "未設定",
        record.code,
        record.user?.id || "",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `daily-winners-${selectedDate.format("YYYY-MM-DD")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-xl font-bold mb-4">查詢每日中獎清單</h2>

      <Space className="mb-4">
        <DatePicker
          value={selectedDate}
          onChange={(date) => date && setSelectedDate(date)}
          format="YYYY-MM-DD"
          placeholder="選擇日期"
        />
        <Button
          type="primary"
          onClick={() => setSelectedDate(dayjs(selectedDate))}
          loading={isLoading}
        >
          查詢
        </Button>
        <Button
          icon={<DownloadOutlined />}
          onClick={exportToCSV}
          disabled={!data || data.length === 0}
        >
          匯出CSV
        </Button>
      </Space>

      <Table
        columns={columns}
        dataSource={data || []}
        loading={isLoading}
        rowKey="id"
        scroll={{ x: 500 }}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 筆記錄`,
        }}
      />
    </div>
  );
};
